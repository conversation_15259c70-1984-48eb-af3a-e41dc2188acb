一、根据模型输入：

1.修改客户表，增加客户等级，类型为数字，并删除与订单表的关联
2.启用订单表逻辑删除，更新表名为 Customer_O2M_Order
      
修改ORM结构（以下为修改后）：
- entityName: "客户"
  intents:
    - intentType: "ADD_ENTITY"
      targetConceptName: "客户"
      props:
        name: "客户"

    - intentType: "ADD_COLUMN"
      targetConceptName: "客户"
      props:
        name: "客户等级"
        stdSqlType: "INT"

    - intentType: "DELETE_RELATION"
      targetConceptName: "客户"
      props:
        from: "客户"
        to: "订单"

- entityName: "订单"
  intents:
    - intentType: "ADD_ENTITY"
      targetConceptName: "订单"
      props:
        name: "订单"

    - intentType: "UPDATE_ENTITY"
      targetConceptName: "订单"
      props:
        useLogicalDelete: true

    - intentType: "UPDATE_ENTITY"
      targetConceptName: "订单"
      props:
        tableName: "Customer_O2M_Order"

    - intentType: "ADD_COLUMN"
      targetConceptName: "订单"
      props:
        name: "订单金额"
        stdSqlType: "DECIMAL"

    - intentType: "ADD_COLUMN"
      targetConceptName: "订单"
      props:
        name: "租赁时长"
        stdSqlType: "INT"

二、合成训练样本格式：

{
  "instruction": "修改客户表，增加客户等级，类型为数字，并删除与订单表的关联。",
  "intent": [
    {
      "intentType": "ADD_COLUMN",
      "targetConceptName": "客户",
      "props": {
        "name": "客户等级",
        "stdSqlType": "INT"
      }
    },
    {
      "intentType": "DELETE_RELATION",
      "targetConceptName": "客户",
      "props": {
        "from": "客户",
        "to": "订单"
      }
    }
  ]
}

三、数据增广：
1. 请在客户实体添加一个整型字段“客户等级”，同时删除其与订单表的关联。
2. 给客户表加一个叫“客户等级”的数字字段，并取消它和订单之间的关系。
3. 添加一个名为客户等级的字段到客户实体中（类型为整数），然后断开客户和订单表之间的连接。
4. 客户表需要一个整型字段“客户等级”，并且要取消与订单表的外键关系。
5. 删除客户和订单的关系，并新增一个“客户等级”字段，数据类型是 INT。

四、每个增广表达与原始意图绑定成一个训练样本：
{
  "instruction": "请在客户实体添加一个整型字段“客户等级”，同时删除其与订单表的关联。",
  "intent": [...同上 intent JSON...]
}

五、去重和评估
1.删除语义重复度过高的表达（比如“加一个字段”和“添加一个字段”意思太接近就去一个）；
2.剔除语法不通顺、结构混乱的问法；

六、生成对抗样本和负样本：
{
  "instruction": "添加字段等级，类型为字符串。",
  "intent": [
    {
      "intentType": "ADD_COLUMN",
      "targetConceptName": "客户",
      "props": {
        "name": "等级",
        "stdSqlType": "STRING"  // 不规范，应为 VARCHAR
      }
    }
  ],
  "label": "对抗样本-类型错误"
}

{
  "instruction": "加一个字段叫 custGrade。",
  "intent": [],
  "label": "负样本-字段别名缺上下文"
}


七、整理和批量生成训练文件
。。。

八、可能的方案
1.目前用提示词可以实现，证明模型是能理解其语义的，这样可以采用COT软标签的方式调用在大模型Api进行数据蒸馏。
2.在我的情形下，场景->意图已经确认，辅以语义、表达、负样本等策略，通过构建提示词应该能构造生成出用户写出的自然语言指令，通过微调确认边界。
3.对每个业务域生成的数据集，对其使用 LoRA 微调 Qwen3-4b 或者 Qwen2.5-7B-Instruct 模型得到微调文件。
4.用户发起自然语言请求，使用门控机制对业务场景(**管理系统等等)区分并路由到某个Adapter，让模型加载Adapter解析用户自然语言得到数据答案集范围之内的某些意图。


九、可能的语义生成策略

需要让模型学习从简单到复杂的指令，理解上下文、隐含意图和多步骤操作。因此设计 4个复杂度等级 (Complexity Levels) 的生成策略。
Level 1: 原子操作 (semantic_matrix.intent_strategy.atomic)
*   目标: 学习最基础的 “意图-坐标” 映射。
*   组合方式: 每个 prompt 只包含 1个 任务 `(1 Intent, 1 Coordinate)`。
*   例子:
    *   Prompt: "在用户实体里，添加一个字符串类型的字段，叫做'email'。"
    *   对应任务: `('CREATE', 'orm.entity.column')`
    *   Completion: 生成包含这个新字段的完整 `orm.entity` JSON 结构。

Level 2: 复合操作 (semantic_matrix.intent_strategy.composite)
*   目标: 学习在一个指令中处理多个同类或不同类的操作。
*   组合方式:
    *   A. 单意图多目标: `(1 Intent, N Coordinates)`
    *   B. 多意图单目标: `(N Intents, 1 Coordinate)`
*   例子:
    *   Prompt: "创建一个'订单'实体，包含ID、订单号、金额三个字段，并设置ID为主键。"
    *   对应任务: `('CREATE', 'orm.entity')` + `('CREATE', 'orm.entity.column')` x3 + `('UPDATE', 'orm.entity.column.primary')`
    *   Completion: 生成包含新实体和字段的完整 `orm` JSON 结构。

Level 3: 序列/事务性操作 (semantic_matrix.intent_strategy.sequence)
*   目标: 学习理解有先后顺序的指令，并处理对象间的关系。
*   例子:
    *   Prompt: "先创建一个'用户'实体，再创建一个'地址'实体，然后在'地址'实体里添加一个到'用户'的多对一关系，通过'user_id'字段关联。"
    *   对应任务: `('CREATE', 'orm.entity')` x2 + `('CREATE', 'orm.entity.relation.toOne')`
    *   Completion: 生成包含两个实体和它们之间关系的完整 `orm` JSON 结构。

Level 4: 隐含与上下文操作 (semantic_matrix.intent_strategy.implicit)
*   目标: 让模型具备“常识”和推理能力，能理解不那么直接的指令。
*   例子:
    *   Prompt: "我需要保证用户的邮箱地址都是唯一的。"
    *   隐含任务: `('CREATE', 'orm.entity.uniqueKey', columns='email')`
    *   Prompt: "为了查询方便，给订单创建时间加个索引。"
    *   隐含任务: `('CREATE', 'orm.entity.index', columns='create_time')`
    *   Completion: 生成应用了这些修改后的 `orm` 结构。